//! HTTP多线程分片下载器

use std::path::Path;
use std::sync::Arc;
use std::time::Duration;
use anyhow::{Result, anyhow};
use futures::StreamExt;
use tokio::fs::OpenOptions;
use tokio::io::{AsyncSeekExt, AsyncWriteExt, SeekFrom};
use tokio::sync::Semaphore;
use tokio::time::sleep;
use tracing::{debug, error, warn, info};

use super::downloader::HttpDownloader;
use super::chunk_manager::{ChunkManager, ChunkDownloadInfo, ChunkStatus};

impl HttpDownloader {
    /// 多线程分片下载
    pub async fn multi_thread_download(&mut self, temp_file_path: &str) -> Result<()> {
        // 检查是否支持范围请求和文件大小已知
        if !self.supports_range || self.total_size.is_none() {
            return Err(anyhow!("Multi-thread download requires range support and known file size"));
        }

        let total_size = self.total_size.unwrap();
        let settings = self.config_manager.get_settings().await;
        
        // 获取配置参数
        let max_concurrent = settings.download.connections_per_download as usize;
        let max_retries = crate::config::constants::MAX_RETRIES;
        let chunk_size = self.chunk_size;

        info!("Starting multi-thread download: {} threads, chunk size: {} bytes", 
              max_concurrent, chunk_size);

        // 创建分片管理器
        let chunk_manager = Arc::new(ChunkManager::new(
            self.task_id,
            total_size,
            chunk_size,
            max_concurrent,
            max_retries,
        ));

        // 初始化分片
        chunk_manager.initialize_chunks().await?;

        // 如果有恢复点，加载分片状态
        if let Some(resume_point) = &self.resume_point {
            chunk_manager.load_from_resume_point(&resume_point.chunks).await?;
        }

        // 创建或打开临时文件
        let mut file = OpenOptions::new()
            .read(true)
            .write(true)
            .create(true)
            .open(temp_file_path)
            .await?;

        // 预分配文件空间
        file.set_len(total_size).await?;

        // 创建信号量控制并发数
        let semaphore = Arc::new(Semaphore::new(max_concurrent));

        // 启动下载调度器
        let scheduler_handle = {
            let chunk_manager = chunk_manager.clone();
            let downloader = self.clone();
            let semaphore = semaphore.clone();
            let temp_file_path = temp_file_path.to_string();
            
            tokio::spawn(async move {
                Self::download_scheduler(downloader, chunk_manager, semaphore, temp_file_path).await
            })
        };

        // 启动进度监控器
        let progress_handle = {
            let chunk_manager = chunk_manager.clone();
            let task_id = self.task_id;
            
            tokio::spawn(async move {
                Self::progress_monitor(chunk_manager, task_id).await
            })
        };

        // 等待下载完成
        let scheduler_result = scheduler_handle.await;
        progress_handle.abort();

        match scheduler_result {
            Ok(Ok(())) => {
                info!("Multi-thread download completed successfully");
                Ok(())
            },
            Ok(Err(e)) => {
                error!("Multi-thread download failed: {}", e);
                Err(e)
            },
            Err(e) => {
                error!("Download scheduler panicked: {}", e);
                Err(anyhow!("Download scheduler failed: {}", e))
            }
        }
    }

    /// 下载调度器
    async fn download_scheduler(
        downloader: HttpDownloader,
        chunk_manager: Arc<ChunkManager>,
        semaphore: Arc<Semaphore>,
        temp_file_path: String,
    ) -> Result<()> {
        loop {
            // 检查是否暂停或取消
            if downloader.is_paused || downloader.is_cancelled {
                debug!("Download paused or cancelled, stopping scheduler");
                break;
            }

            // 检查是否所有分片都已完成
            if chunk_manager.is_all_completed().await {
                info!("All chunks completed");
                break;
            }

            // 检查是否可以启动新的下载
            if !chunk_manager.can_start_new_download().await {
                sleep(Duration::from_millis(100)).await;
                continue;
            }

            // 获取下一个待下载的分片
            if let Some(chunk_info) = chunk_manager.get_next_pending_chunk().await {
                // 获取信号量许可
                let permit = semaphore.clone().acquire_owned().await?;
                
                // 标记分片为正在下载
                chunk_manager.mark_chunk_downloading(chunk_info.index).await?;

                // 启动分片下载任务
                let chunk_manager_clone = chunk_manager.clone();
                let downloader_clone = downloader.clone();
                let temp_file_path_clone = temp_file_path.clone();
                
                let download_handle = tokio::spawn(async move {
                    let result = Self::download_chunk_worker(
                        downloader_clone,
                        chunk_info,
                        temp_file_path_clone,
                        chunk_manager_clone.clone(),
                    ).await;
                    
                    // 释放信号量许可
                    drop(permit);
                    
                    // 移除活动下载任务
                    chunk_manager_clone.remove_active_download(chunk_info.index).await;
                    
                    result
                });

                // 添加到活动下载任务
                chunk_manager.add_active_download(chunk_info.index, download_handle).await;
            } else {
                // 没有待下载的分片，等待一段时间
                sleep(Duration::from_millis(100)).await;
            }
        }

        Ok(())
    }

    /// 分片下载工作器
    async fn download_chunk_worker(
        downloader: HttpDownloader,
        mut chunk_info: ChunkDownloadInfo,
        temp_file_path: String,
        chunk_manager: Arc<ChunkManager>,
    ) -> Result<()> {
        debug!("Starting download for chunk {}: {}-{}", 
               chunk_info.index, chunk_info.start, chunk_info.end);

        let start_time = std::time::Instant::now();
        let mut downloaded_bytes = 0u64;

        // 创建下载流
        let mut stream = match downloader.download_chunk(chunk_info.start, Some(chunk_info.end)).await {
            Ok(s) => s,
            Err(e) => {
                let error_msg = format!("Failed to create download stream: {}", e);
                chunk_manager.mark_chunk_failed(chunk_info.index, error_msg.clone()).await?;
                return Err(anyhow!(error_msg));
            }
        };

        // 打开文件进行写入
        let mut file = match OpenOptions::new()
            .write(true)
            .open(&temp_file_path)
            .await {
            Ok(f) => f,
            Err(e) => {
                let error_msg = format!("Failed to open file for writing: {}", e);
                chunk_manager.mark_chunk_failed(chunk_info.index, error_msg.clone()).await?;
                return Err(anyhow!(error_msg));
            }
        };

        // 定位到分片起始位置
        file.seek(SeekFrom::Start(chunk_info.start)).await?;

        // 下载数据
        while let Some(chunk_result) = stream.next().await {
            match chunk_result {
                Ok(chunk) => {
                    let chunk_size = chunk.len() as u64;
                    
                    // 写入数据
                    if let Err(e) = file.write_all(&chunk).await {
                        let error_msg = format!("Failed to write chunk data: {}", e);
                        chunk_manager.mark_chunk_failed(chunk_info.index, error_msg.clone()).await?;
                        return Err(anyhow!(error_msg));
                    }

                    downloaded_bytes += chunk_size;
                    
                    // 计算下载速度
                    let elapsed = start_time.elapsed().as_secs_f64();
                    let speed = if elapsed > 0.0 {
                        (downloaded_bytes as f64 / elapsed) as u64
                    } else {
                        0
                    };

                    // 更新进度
                    chunk_manager.update_chunk_progress(chunk_info.index, downloaded_bytes, speed).await?;

                    // 检查是否下载完成
                    if downloaded_bytes >= chunk_info.size() {
                        break;
                    }
                },
                Err(e) => {
                    let error_msg = format!("Stream error: {}", e);
                    chunk_manager.mark_chunk_failed(chunk_info.index, error_msg.clone()).await?;
                    return Err(anyhow!(error_msg));
                }
            }
        }

        // 刷新文件
        file.flush().await?;

        // 验证下载完整性
        if downloaded_bytes == chunk_info.size() {
            // TODO: 计算校验和
            chunk_manager.mark_chunk_completed(chunk_info.index, None).await?;
            debug!("Chunk {} download completed: {} bytes", chunk_info.index, downloaded_bytes);
        } else {
            let error_msg = format!("Incomplete download: expected {}, got {}", 
                                   chunk_info.size(), downloaded_bytes);
            chunk_manager.mark_chunk_failed(chunk_info.index, error_msg.clone()).await?;
            return Err(anyhow!(error_msg));
        }

        Ok(())
    }

    /// 进度监控器
    async fn progress_monitor(chunk_manager: Arc<ChunkManager>, task_id: uuid::Uuid) -> Result<()> {
        let mut last_report = std::time::Instant::now();
        
        loop {
            sleep(Duration::from_secs(1)).await;

            let (downloaded, total, percentage) = chunk_manager.get_progress().await;
            let speed = chunk_manager.get_speed().await;
            let stats = chunk_manager.get_statistics().await;

            // 每5秒报告一次详细进度
            if last_report.elapsed() >= Duration::from_secs(5) {
                info!("Task {} progress: {:.2}% ({}/{} bytes), speed: {} KB/s, chunks: {} pending, {} downloading, {} completed, {} failed",
                      task_id,
                      percentage,
                      downloaded,
                      total,
                      speed / 1024,
                      stats.get("pending").unwrap_or(&0),
                      stats.get("downloading").unwrap_or(&0),
                      stats.get("completed").unwrap_or(&0),
                      stats.get("failed").unwrap_or(&0)
                );
                last_report = std::time::Instant::now();
            }

            // 检查是否完成
            if chunk_manager.is_all_completed().await {
                break;
            }
        }

        Ok(())
    }
}
